/*
TM1640R.h - Library for TM1640.

Copyright (C) 2011 <PERSON> <rjbatista at gmail dot com>
Adjusted for TM1640 by Maxint R&D, based on orignal code. See https://github.com/maxint-rd/

This program is free software: you can redistribute it and/or modify
it under the terms of the version 3 GNU General Public License as
published by the Free Software Foundation.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

#ifndef TM1640R_h
#define TM1640R_h

#if defined(ARDUINO) && ARDUINO >= 100
	#include "Arduino.h"
#else
	#include "WProgram.h"
#endif

#include "TM16xx.h"

#define TM1640_MAX_POS 16

// TM1640字库，错位的重新编排
// A=5
// B=7
// C=0
// D=1
// E=3
// F=4
// G=6
// M=2
/*
m=6
c=4
d=5
e=7
b=3
a=1
f=0
g=2
0b11100011
0b01111010
	Segment labels
	 -- A --
	|       |
	F       B
	 -- G --
	E       C
	|       |
	 -- D --  .DP

	The bits are displayed by the mapping below:
	 -- 5 --
	|       |
	4       7
	 -- 6 --
	3       0
	|       |
	 -- 1 --  .2

*/
const PROGMEM byte TM1640_NUMBER_FONT[] = {
	0b10111011, // 0
	0b10000001, // 1
	0b11101010, // 2
	0b11100011, // 3
	0b11010001, // 4
	0b01110011, // 5
	0b01111011, // 6
	0b10100001, // 7
	0b11111011, // 8
	0b11110011, // 9
	0b11111001, // A
	0b01011011, // b
	0b00111010, // C
	0b11001011, // d
	0b01111010, // E
	0b01111000, // F
	0b01111011, // G
	0b11011001, // H
};

class TM1640R : public TM16xx
{
  public:
		// Instantiate a TM1640 module specifying data and clock pins, number of digits, the display state, the starting intensity (0-7).
  	TM1640R(byte dataPin, byte clockPin, byte numDigits=16, bool activateDisplay = true, byte intensity = 7);
	virtual void setDisplayToString(const char* string, const word dots=0, const byte pos=0, const byte font[] = TM1640_NUMBER_FONT);

  protected:
    //virtual void bitDelay();
    virtual void start();
    virtual void stop();
    virtual void send(byte data);
};

#endif
