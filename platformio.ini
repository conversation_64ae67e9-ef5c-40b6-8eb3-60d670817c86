; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32-c3-devkitm-1]
board = esp32-c3-devkitm-1
platform = espressif32
framework = arduino
monitor_speed = 9600
build_flags = 
	-D ARDUINO_USB_MODE=1
	-D ARDUINO_USB_CDC_ON_BOOT=1
lib_deps =
	paulstoffregen/Time@^1.6.1
	maxint-rd/TM16xx LEDs and Buttons@^0.7.2412
	adafruit/Adafruit AHTX0@^2.0.5
