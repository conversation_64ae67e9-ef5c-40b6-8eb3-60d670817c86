<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CMakePythonSetting">
    <option name="pythonIntegrationState" value="YES" />
  </component>
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="PlatformIOSettings">
    <option name="linkedExternalProjectsSettings">
      <PlatformioProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$" />
          </set>
        </option>
      </PlatformioProjectSettings>
    </option>
  </component>
  <component name="PlatformIOWorkspace" PROJECT_DIR="$PROJECT_DIR$" />
</project>