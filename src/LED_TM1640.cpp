#include <Adafruit_AHTX0.h>
#include <Arduino.h>
#include <TM1640R.h>
#include <TimeLib.h>
#include <Wire.h>
#include <WiFi.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

TM1640R module(10, 20, 16);

// 温湿度
Adafruit_AHTX0 aht;
sensors_event_t humidity, temp;
char temperature[4];  // 修复：增加缓冲区大小，避免溢出
char humidityVal[4];  // 修复：增加缓冲区大小，避免溢出
void AHT_Update();

// 时钟
char timeYear[8];
char timeMonth[8];
char timeDate[8];
char timeHour[8];
char timeMinute[8];
char timeSecond[8];
byte daysOfTheWeek[7] = {7, 1, 2, 3, 4, 5, 6};
byte timeWeek;

// WiFi
const char* ssid = "ky2"; // Wifi Network
const char* password = "23719252"; // Wifi Password

void connectToWiFi();
void scanWIFI();
void checkWiFiConnection(void *pvParameters);  // 新增WiFi监控任务

WiFiUDP Udp;
unsigned int localPort = 8888;
static const char ntpServerName[] = "cn.pool.ntp.org";
const int timeZone = +8;     // Central European Time

time_t getNtpTime();
void sendNTPpacket(IPAddress &address);
void refreshTime(void *pvParameters);
void refreshAHT(void *pvParameters);

void setup()
{
    pinMode(LED_BUILTIN, OUTPUT);
    Serial.begin(9600);
    delay(50);

    // scanWIFI();
    connectToWiFi();

    module.setDisplayToString("8888888888888888");
    delay(50);
    module.clearDisplay();
    module.sendChar(1, 0b00000100, false);
    module.sendChar(3, 0b00000100, false);
    module.sendChar(12, 0b01111010, false);
    module.sendChar(15, 0b11011001, false);

    // 优化任务堆栈大小
    xTaskCreate(
      refreshTime,   /* Task函数 */
      "refreshTime", /* 任务名用于调试 */
      4096,          /* 堆栈大小优化：减少内存使用 */
      NULL,          /* 传递给任务的参数 */
      2,             /* 任务优先级：时间显示优先级提高 */
      NULL           /* 任务句柄 */
    );

    xTaskCreate(refreshAHT, "refreshAHT", 4096, NULL, 1, NULL);  // 优化堆栈大小和优先级

    // 创建WiFi监控任务
    xTaskCreate(checkWiFiConnection, "checkWiFi", 4096, NULL, 0, NULL);  // 优化堆栈大小

    Serial.println("setup ok!");
}

bool b;
void loop()
{
    if (b=!b)
    {
        digitalWrite(LED_BUILTIN, HIGH);
        module.sendChar(7, 0b00100010, false);
    }
    else
    {
        digitalWrite(LED_BUILTIN, LOW);
        // 秒表闪烁
        module.clearDisplayDigit(7, false);
    }

    delay(500);
}

void refreshTime(void *pvParameters)
{
    TickType_t lasttick;
    lasttick = xTaskGetTickCount();
    while(1)
    {
        // time
        time_t t = now();
        sprintf(timeYear, "%02d", year(t));
        sprintf(timeMonth, "%02d", month(t));
        sprintf(timeDate, "%02d", day(t));
        sprintf(timeHour, "%02d", hour(t));
        sprintf(timeMinute, "%02d", minute(t));
        sprintf(timeSecond, "%02d", second(t));
        timeWeek = daysOfTheWeek[weekday(t) - 1];

        Serial.print("time Year: ");
        Serial.print(timeYear);
        Serial.print(" time Month: ");
        Serial.print(timeMonth);
        Serial.print(" time Date: ");
        Serial.print(timeDate);
        Serial.print(" time Hour: ");
        Serial.print(timeHour);
        Serial.print(" time Minute: ");
        Serial.print(timeMinute);
        Serial.print(" Second: ");
        Serial.print(timeSecond);
        Serial.print(" Week: ");
        Serial.println(timeWeek);
        Serial.print(" timestamp: ");
        Serial.println(t);

        // module.setDisplayToString(timeYear, true, 10);
        module.setDisplayToString(timeMonth, true, 0);
        module.setDisplayToString(timeDate, true, 2);
        module.setDisplayToString(timeHour, false, 5);
        module.setDisplayToString(timeMinute, false, 8);
        // module.setDisplayToString(timeSecond, false, 13);
        module.setDisplayDigit(timeWeek, 4, false, TM1640_NUMBER_FONT);

        //相对于lasttick 1000ms后重新执行操作
        vTaskDelayUntil(&lasttick, 1000 / portTICK_PERIOD_MS);
    }

}

void refreshAHT(void *pvParameters) {
    Wire.begin(9, 21);
    aht.begin(&Wire, 0, 0x38);
    while(1) {
        if (aht.getStatus() == 0xFF) {
            Serial.println("Could not find AHT? Check wiring");
            module.setDisplayToString("00", false, 10);
            module.setDisplayToString("00", false, 13);
            if (aht.begin(&Wire, 0, 0x38))
            {
                Serial.println("AHT10 or AHT20 found");
                vTaskDelay(100 / portTICK_PERIOD_MS);
                continue;
            }
        } else
        {
            aht.getEvent(&humidity, &temp);

            // 增加边界检查和数据验证
            float tempValue = temp.temperature;
            float humidityValue = humidity.relative_humidity;

            // 温度范围检查 (-40°C 到 85°C)
            if (tempValue < -40 || tempValue > 85) {
                strcpy(temperature, "Er");
            } else {
                snprintf(temperature, sizeof(temperature), "%2.0f", tempValue);
            }

            // 湿度范围检查 (0% 到 100%)
            if (humidityValue < 0 || humidityValue > 100) {
                strcpy(humidityVal, "Er");
            } else {
                snprintf(humidityVal, sizeof(humidityVal), "%2.0f", humidityValue);
            }

            module.setDisplayToString(temperature, false, 10);
            module.setDisplayToString(humidityVal, false, 13);
            module.sendChar(12, 0b01111010, false);  // 'C' 符号
            module.sendChar(15, 0b11011001, false);  // 'H' 符号
        }
        vTaskDelay(3000 / portTICK_PERIOD_MS); // 等待3秒
    }
}

/*-------- WIFI code ----------*/
void connectToWiFi() {
    Serial.println("Connecting to WiFi...");
    WiFi.mode(WIFI_STA); //设置为STA模式
    WiFi.setTxPower(WIFI_POWER_8_5dBm);
    WiFi.disconnect();   //断开当前可能的连接
    WiFi.begin(ssid, password);

    char ssidStr[32];
    sprintf(ssidStr, "ssid: %s, password: %s", ssid , password);
    Serial.println(ssidStr);
    int attempts = 0;
    while (WiFi.status() != WL_CONNECTED && attempts < 8) {
        delay(200);
        Serial.print(".");
        attempts++;
    }
    Serial.println(WiFi.status());
    if (WiFi.status() == WL_CONNECTED) {
        Serial.println("WiFi connected.");
        Serial.print("Local IP:");
        Serial.println(WiFi.localIP());

        Serial.print("IP number assigned by DHCP is ");
        Serial.println(WiFi.localIP());
        Serial.println("Starting UDP");
        Udp.begin(localPort);
        // Serial.print("Local port: ");
        // Serial.println(Udp.localPort());
        Serial.println("waiting for sync");
        setSyncProvider(getNtpTime);
        setSyncInterval(300);
    } else {
        Serial.println("WiFi connection failed.");
        Serial.println("Skipping to sleep.");
        // digitalWrite(WIFI_STATE_PIN, LOW);
        // esp_sleep_enable_timer_wakeup(WAKE_UP_INTERVAL_SECONDS * 1000000);
        // esp_deep_sleep_start();
    }
}

void scanWIFI()
{
    WiFi.mode(WIFI_STA); //设置为STA模式
    WiFi.setTxPower(WIFI_POWER_8_5dBm);
    WiFi.disconnect();   //断开当前可能的连接
    delay(100);

    Serial.println("scan start");
    int n = WiFi.scanNetworks(); //扫描并返回搜索到的网络数量，该方法默认会阻塞
    Serial.println("scan done");
    if (n != 0)
    {
        Serial.print(n);
        Serial.println(" networks found");
        for (int i = 0; i < n; ++i)
        {
            Serial.println();
            Serial.print(i + 1);
            Serial.print(":       ");
            Serial.print(WiFi.SSID(i)); //打印网络名称
            Serial.print("        ");
            Serial.print(WiFi.RSSI(i)); //打印信号强度
            Serial.print("        ");
            Serial.print((WiFi.encryptionType(i) == WIFI_AUTH_OPEN) ? "未加密" : "加密"); //打印是否加密
            delay(10);
        }
    }
}


/*-------- NTP code ----------*/
const int NTP_PACKET_SIZE = 48; // NTP time is in the first 48 bytes of message
byte packetBuffer[NTP_PACKET_SIZE]; //buffer to hold incoming & outgoing packets

time_t getNtpTime()
{
  static int ntpRetryCount = 0;  // 添加重试计数器
  const int MAX_NTP_RETRIES = 3;  // 最大重试次数

  IPAddress ntpServerIP; // NTP server's ip address

  while (Udp.parsePacket() > 0) ; // discard any previously received packets
  Serial.println("Transmit NTP Request");

  // get a random server from the pool
  if (!WiFi.hostByName(ntpServerName, ntpServerIP)) {
    Serial.println("DNS lookup failed");
    return 0;
  }

  Serial.print(ntpServerName);
  Serial.print(": ");
  Serial.println(ntpServerIP);
  sendNTPpacket(ntpServerIP);
  uint32_t beginWait = millis();
  while (millis() - beginWait < 1500) {
    int size = Udp.parsePacket();
    if (size >= NTP_PACKET_SIZE) {
      Serial.println("Receive NTP Response");
      Udp.read(packetBuffer, NTP_PACKET_SIZE);  // read packet into the buffer
      unsigned long secsSince1900;
      // convert four bytes starting at location 40 to a long integer
      secsSince1900 =  (unsigned long)packetBuffer[40] << 24;
      secsSince1900 |= (unsigned long)packetBuffer[41] << 16;
      secsSince1900 |= (unsigned long)packetBuffer[42] << 8;
      secsSince1900 |= (unsigned long)packetBuffer[43];
      unsigned long s = secsSince1900 - 2208988800UL + timeZone * SECS_PER_HOUR;
      Serial.println(s);
      ntpRetryCount = 0;  // 重置重试计数器
      return s;
    }
  }

  Serial.println("No NTP Response :-(");
  // 已经修复了递归调用问题，这里直接返回0
  return 0;
}

// send an NTP request to the time server at the given address
void sendNTPpacket(IPAddress &address)
{
  // set all bytes in the buffer to 0
  memset(packetBuffer, 0, NTP_PACKET_SIZE);
  // Initialize values needed to form NTP request
  // (see URL above for details on the packets)
  packetBuffer[0] = 0b11100011;   // LI, Version, Mode
  packetBuffer[1] = 0;     // Stratum, or type of clock
  packetBuffer[2] = 6;     // Polling Interval
  packetBuffer[3] = 0xEC;  // Peer Clock Precision
  // 8 bytes of zero for Root Delay & Root Dispersion
  packetBuffer[12] = 49;
  packetBuffer[13] = 0x4E;
  packetBuffer[14] = 49;
  packetBuffer[15] = 52;
  // all NTP fields have been given values, now
  // you can send a packet requesting a timestamp:
  Udp.beginPacket(address, 123); //NTP requests are to port 123
  Udp.write(packetBuffer, NTP_PACKET_SIZE);
  Udp.endPacket();
}

/*-------- WiFi监控重连代码 ----------*/
void checkWiFiConnection(void *pvParameters) {
    const int WIFI_CHECK_INTERVAL = 10000;  // 10秒检查间隔
    const int WIFI_RETRY_DELAY = 30000;     // 重连失败后等待30秒
    static int consecutiveFailures = 0;      // 连续失败次数

    while(1) {
        // 每10秒检查一次WiFi连接状态
        vTaskDelay(WIFI_CHECK_INTERVAL / portTICK_PERIOD_MS);

        if (WiFi.status() != WL_CONNECTED) {
            Serial.println("WiFi disconnected! Attempting to reconnect...");
            consecutiveFailures++;

            // 尝试重连WiFi
            WiFi.disconnect();
            vTaskDelay(1000 / portTICK_PERIOD_MS);  // 使用vTaskDelay替代delay

            WiFi.begin(ssid, password);

            int attempts = 0;
            while (WiFi.status() != WL_CONNECTED && attempts < 10) {
                vTaskDelay(500 / portTICK_PERIOD_MS);  // 使用vTaskDelay替代delay
                Serial.print(".");
                attempts++;
            }

            if (WiFi.status() == WL_CONNECTED) {
                Serial.println("\nWiFi reconnected successfully!");
                Serial.print("Local IP: ");
                Serial.println(WiFi.localIP());
                consecutiveFailures = 0;  // 重置失败计数

                // 重新初始化UDP和NTP
                Udp.stop();
                Udp.begin(localPort);
                setSyncProvider(getNtpTime);
                setSyncInterval(300);
            } else {
                Serial.printf("\nWiFi reconnection failed (%d consecutive failures). Will retry in 10 seconds.\n", consecutiveFailures);

                // 如果连续失败多次，增加等待时间
                if (consecutiveFailures >= 3) {
                    Serial.println("Multiple failures detected, waiting 30 seconds before next check...");
                    vTaskDelay(WIFI_RETRY_DELAY / portTICK_PERIOD_MS);
                }
            }
        } else {
            if (consecutiveFailures > 0) {
                Serial.println("WiFi connection restored");
                consecutiveFailures = 0;
            }
            // 减少正常状态下的日志输出频率
            // Serial.println("WiFi connection OK");
        }
    }
}